<template>
  <Modal ref="ModalRef" title="设备数量统计">
    <template #title>
      <modelIconTitle class="icon-title" />
      设备数量统计
    </template>
    <div class="statics">
      <div
        v-for="item in props.data"
        :key="item.deviceType"
        class="statics-item"
        :class="{ 'is-active': listQuery.deviceType == item.deviceType }"
        @click="onClick(item)"
      >
        <div class="statics-item-icon-container">
          <component :is="deviceTypeMap[item.deviceType].icon" class="statics-item-icon" />
        </div>
        <div class="statics-item-text">
          <div class="statics-item-text-total">
            <div class="statics-item-text-number">{{ item.totalNumber }}</div>
            <div class="statics-item-text-unit">个</div>
          </div>
          <div class="statics-item-text-label">
            {{ deviceTypeMap[item.deviceType].label }}
          </div>
        </div>
        <div class="border border-left-top" />
        <div class="border border-left-bottom" />
        <div class="border border-right-top" />
        <div class="border border-right-bottom" />
        <div class="dot dot-left-top"></div>
        <div class="dot dot-right-bottom"></div>
      </div>
    </div>
    <div class="table-container">
      <div class="table-title">
        {{ deviceTypeMap[listQuery.deviceType]?.label || "设备" }}设备台账
        <modelDivider class="icon-divider" />
      </div>
      <div class="table-content">
        <a-table
          class="flex-table"
          :columns="columns"
          :data="listData"
          :scroll="{
            x: '100%',
            y: '100%',
          }"
          :pagination="false"
        >
          <template #statusTitle>
            <div class="status-title">
              <span>设备状态</span>
              <div class="custom-dropdown" @click.stop="toggleStatusDropdown">
                <img src="@/assets/images/detail/filter.png" alt="filter" class="filter-icon" />
                <!-- 自定义下拉菜单 -->
                <div v-if="showStatusDropdown" class="custom-dropdown-menu" @click.stop>
                  <div class="dropdown-options">
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedStatus.includes(1) }"
                      @click="toggleStatusOption(1)"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedStatus.includes(1) }"
                      >
                        <svg
                          v-if="tempSelectedStatus.includes(1)"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">在线</span>
                    </div>
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedStatus.includes(0) }"
                      @click="toggleStatusOption(0)"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedStatus.includes(0) }"
                      >
                        <svg
                          v-if="tempSelectedStatus.includes(0)"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">离线</span>
                    </div>
                  </div>
                  <div class="dropdown-actions">
                    <button class="action-btn reset-btn" @click="resetSelection">重置</button>
                    <button class="action-btn confirm-btn" @click="confirmSelection">确定</button>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 方向列标题 -->
          <template #directionTitle>
            <div class="status-title">
              <span>方向</span>
              <div class="custom-dropdown" @click.stop="toggleDirectionDropdown">
                <img src="@/assets/images/detail/filter.png" alt="filter" class="filter-icon" />
                <!-- 方向筛选下拉菜单 -->
                <div v-if="showDirectionDropdown" class="custom-dropdown-menu" @click.stop>
                  <div class="dropdown-options">
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedDirection.includes(1) }"
                      @click="toggleDirectionOption(1)"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedDirection.includes(1) }"
                      >
                        <svg
                          v-if="tempSelectedDirection.includes(1)"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">那曲方向</span>
                    </div>
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedDirection.includes(2) }"
                      @click="toggleDirectionOption(2)"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedDirection.includes(2) }"
                      >
                        <svg
                          v-if="tempSelectedDirection.includes(2)"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">格尔木方向</span>
                    </div>
                  </div>
                  <div class="dropdown-actions">
                    <button class="action-btn reset-btn" @click="resetDirectionSelection">
                      重置
                    </button>
                    <button class="action-btn confirm-btn" @click="confirmDirectionSelection">
                      确定
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 分部列标题 -->
          <template #departmentTitle>
            <div class="status-title">
              <span>分部</span>
              <div class="custom-dropdown" @click.stop="toggleDepartmentDropdown">
                <img src="@/assets/images/detail/filter.png" alt="filter" class="filter-icon" />
                <!-- 分部筛选下拉菜单 -->
                <div v-if="showDepartmentDropdown" class="custom-dropdown-menu" @click.stop>
                  <div class="dropdown-options">
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedDepartment.includes('gg1') }"
                      @click="toggleDepartmentOption('gg1')"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedDepartment.includes('gg1') }"
                      >
                        <svg
                          v-if="tempSelectedDepartment.includes('gg1')"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">格贡一分部</span>
                    </div>
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedDepartment.includes('gg2') }"
                      @click="toggleDepartmentOption('gg2')"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedDepartment.includes('gg2') }"
                      >
                        <svg
                          v-if="tempSelectedDepartment.includes('gg2')"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">格贡二分部</span>
                    </div>
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedDepartment.includes('gg3') }"
                      @click="toggleDepartmentOption('gg3')"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedDepartment.includes('gg3') }"
                      >
                        <svg
                          v-if="tempSelectedDepartment.includes('gg3')"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">格贡三分部</span>
                    </div>
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedDepartment.includes('gg4') }"
                      @click="toggleDepartmentOption('gg4')"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedDepartment.includes('gg4') }"
                      >
                        <svg
                          v-if="tempSelectedDepartment.includes('gg4')"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">格贡四分部</span>
                    </div>
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedDepartment.includes('gn1') }"
                      @click="toggleDepartmentOption('gn1')"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedDepartment.includes('gn1') }"
                      >
                        <svg
                          v-if="tempSelectedDepartment.includes('gn1')"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">贡那一分部</span>
                    </div>
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedDepartment.includes('gn2') }"
                      @click="toggleDepartmentOption('gn2')"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedDepartment.includes('gn2') }"
                      >
                        <svg
                          v-if="tempSelectedDepartment.includes('gn2')"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">贡那二分部</span>
                    </div>
                  </div>
                  <div class="dropdown-actions">
                    <button class="action-btn reset-btn" @click="resetDepartmentSelection">
                      重置
                    </button>
                    <button class="action-btn confirm-btn" @click="confirmDepartmentSelection">
                      确定
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 工区列标题 -->
          <template #workAreaTitle>
            <div class="status-title">
              <span>工区</span>
              <div class="custom-dropdown" @click.stop="toggleWorkAreaDropdown">
                <img src="@/assets/images/detail/filter.png" alt="filter" class="filter-icon" />
                <!-- 工区筛选下拉菜单 -->
                <div v-if="showWorkAreaDropdown" class="custom-dropdown-menu" @click.stop>
                  <div class="dropdown-options">
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedWorkArea.includes('一工区') }"
                      @click="toggleWorkAreaOption('一工区')"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedWorkArea.includes('一工区') }"
                      >
                        <svg
                          v-if="tempSelectedWorkArea.includes('一工区')"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">一工区</span>
                    </div>
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedWorkArea.includes('二工区') }"
                      @click="toggleWorkAreaOption('二工区')"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedWorkArea.includes('二工区') }"
                      >
                        <svg
                          v-if="tempSelectedWorkArea.includes('二工区')"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">二工区</span>
                    </div>
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedWorkArea.includes('三工区') }"
                      @click="toggleWorkAreaOption('三工区')"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedWorkArea.includes('三工区') }"
                      >
                        <svg
                          v-if="tempSelectedWorkArea.includes('三工区')"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">三工区</span>
                    </div>
                    <div
                      class="custom-dropdown-option"
                      :class="{ selected: tempSelectedWorkArea.includes('四工区') }"
                      @click="toggleWorkAreaOption('四工区')"
                    >
                      <div
                        class="option-checkbox"
                        :class="{ checked: tempSelectedWorkArea.includes('四工区') }"
                      >
                        <svg
                          v-if="tempSelectedWorkArea.includes('四工区')"
                          class="check-icon"
                          viewBox="0 0 16 16"
                        >
                          <path
                            d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                          />
                        </svg>
                      </div>
                      <span class="option-text">四工区</span>
                    </div>
                  </div>
                  <div class="dropdown-actions">
                    <button class="action-btn reset-btn" @click="resetWorkAreaSelection">
                      重置
                    </button>
                    <button class="action-btn confirm-btn" @click="confirmWorkAreaSelection">
                      确定
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <template #status="{ record }">
            <div class="device-status">
              <div
                class="status-dot"
                :class="{
                  'status-online': record.onlineState === 1,
                  'status-offline': record.onlineState === 0,
                }"
              ></div>
              <span
                class="status-text"
                :class="{
                  'text-online': record.onlineState === 1,
                  'text-offline': record.onlineState === 0,
                }"
              >
                {{ record.onlineState === 1 ? "在线" : "离线" }}
              </span>
            </div>
          </template>
        </a-table>
      </div>
      <!-- 自定义分页组件 -->
      <div class="pagination-container">
        <div class="pagination-info">
          共<span class="pagination-number">{{ totalPages }}</span
          >页/<span class="pagination-number">{{ total }}</span
          >条数据
        </div>
        <div class="pagination">
          <div class="pagination-info">
            共<span class="pagination-number">{{ totalPages }}</span
            >页
          </div>
          <button
            class="pagination-btn prev"
            :disabled="listQuery.page === 1"
            @click="changePage(listQuery.page - 1)"
          >
            <icon-left />
          </button>
          <button
            v-for="page in getPageNumbers()"
            :key="page"
            class="pagination-btn"
            :class="{
              active: page === listQuery.page,
              ellipsis: page === '...',
            }"
            :disabled="page === '...'"
            @click="page !== '...' && changePage(page)"
          >
            {{ page }}
          </button>
          <button
            class="pagination-btn next"
            :disabled="listQuery.page === totalPages"
            @click="changePage(listQuery.page + 1)"
          >
            <icon-right />
          </button>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import request from "@/utils/request";
import modelIconTitle from "@/assets/modules/traffic/icon/model-icon-title.svg?component";
import deviceLight from "@/assets/modules/traffic/icon/device-light.svg?component";
import deviceCamera from "@/assets/modules/traffic/icon/device-camera.svg?component";
import deviceAdjustment from "@/assets/modules/traffic/icon/device-adjustment.svg?component";
import deviceEdge from "@/assets/modules/traffic/icon/device-edge.svg?component";
import modelDivider from "@/assets/modules/traffic/icon/model-divider.svg?component";
import Modal from "./Modal.vue";
import { IconLeft, IconRight } from "@arco-design/web-vue/es/icon";
import { cloneDeep } from "lodash-es";
import { ref, computed, onMounted, onUnmounted, nextTick } from "vue";

const props = defineProps({
  data: {
    type: Array,
    default: [],
  },
});
const deviceTypeMap = {
  7: { icon: deviceCamera, label: "摄像机" },
  12: { icon: deviceLight, label: "信号灯" },
  13: { icon: deviceAdjustment, label: "交调设备" },
  14: { icon: deviceEdge, label: "边缘计算" },
};
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    fixed: "left",
    width: 80,
    align: "center",
    render: ({ rowIndex }) => {
      // 计算跨页连续序号：(当前页码-1) * 每页条数 + 当前行索引 + 1
      return (listQuery.value.page - 1) * listQuery.value.size + rowIndex + 1;
    },
  },
  {
    title: "设备名称",
    dataIndex: "deviceName",
    align: "left",
  },
  {
    title: "设备状态",
    dataIndex: "onlineState",
    slotName: "status",
    titleSlotName: "statusTitle",
    align: "center",
  },
  {
    title: "设备位置",
    dataIndex: "location",
    align: "left",
  },
  {
    title: "方向",
    dataIndex: "direction",
    align: "center",
    titleSlotName: "directionTitle",
    render: ({ record }) => {
      const directionMap = {
        1: "那曲",
        2: "格尔木",
      };
      return directionMap[record.direction] || record.direction;
    },
  },
  {
    title: "分部",
    dataIndex: "departmentId",
    align: "center",
    titleSlotName: "departmentTitle",
  },
  {
    title: "工区",
    dataIndex: "workAreaId",
    align: "center",
    titleSlotName: "workAreaTitle",
  },
];
const ModalRef = ref(null);
const listQuery = ref({
  page: 1,
  size: 10,
  deviceType: "7",
  onlineState: [], // 数组形式，支持多选
  direction: [], // 方向筛选，数组形式
  departmentId: [], // 分部筛选，数组形式
  workAreaId: [], // 工区筛选，数组形式
});
const listData = ref([]);
const total = ref(0);

// 设备状态筛选
const showStatusDropdown = ref(false);
const tempSelectedStatus = ref([]); // 临时选中的状态

// 方向筛选
const showDirectionDropdown = ref(false);
const tempSelectedDirection = ref([]); // 临时选中的方向

// 分部筛选
const showDepartmentDropdown = ref(false);
const tempSelectedDepartment = ref([]); // 临时选中的分部

// 工区筛选
const showWorkAreaDropdown = ref(false);
const tempSelectedWorkArea = ref([]); // 临时选中的工区

// 工区名称到ID的映射
const workAreaNameToId = {
  一工区: -1,
  二工区: -2,
  三工区: -3,
  四工区: -4,
};

const getData = (page = 1) => {
  const params = cloneDeep(listQuery.value);
  params.page = page - 1;

  // 处理多选筛选参数，如果数组为空则不传递该参数（表示查询全部）
  if (params.onlineState.length === 0) {
    delete params.onlineState;
  } else {
    // 将数组转换为逗号分隔的字符串
    params.onlineState = params.onlineState.join(",");
  }

  if (params.direction.length === 0) {
    delete params.direction;
  } else {
    params.direction = params.direction.join(",");
  }

  if (params.departmentId.length === 0) {
    delete params.departmentId;
  } else {
    params.departmentId = params.departmentId.join(",");
  }

  if (params.workAreaId.length === 0) {
    delete params.workAreaId;
  } else {
    // 将工区名称转换为对应的ID，然后转换为逗号分隔的字符串
    const workAreaIds = params.workAreaId.map((name) => workAreaNameToId[name] || name);
    params.workAreaId = workAreaIds.join(",");
  }

  console.log("调用接口，最终查询参数:", params);

  request.get("/api/screen/baotong/device/pagelist", params).then((res) => {
    if (res.code == 200) {
      const { data } = res;
      listData.value = data.records || [];
      total.value = data.total || 0;
    }
  });
};

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(total.value / listQuery.value.size);
});

// 分页页码生成逻辑
const getPageNumbers = () => {
  const current = listQuery.value.page;
  const totalPageCount = totalPages.value;
  const pages = [];

  if (totalPageCount <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= totalPageCount; i++) {
      pages.push(i);
    }
  } else {
    // 总页数大于7时的逻辑
    if (current <= 4) {
      // 当前页在前4页
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
      pages.push("...");
      pages.push(totalPageCount);
    } else if (current >= totalPageCount - 3) {
      // 当前页在后4页
      pages.push(1);
      pages.push("...");
      for (let i = totalPageCount - 4; i <= totalPageCount; i++) {
        pages.push(i);
      }
    } else {
      // 当前页在中间
      pages.push(1);
      pages.push("...");
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i);
      }
      pages.push("...");
      pages.push(totalPageCount);
    }
  }

  return pages;
};

// 切换页码
const changePage = (page) => {
  if (page < 1 || page > totalPages.value) return;
  listQuery.value.page = page;
  getData(page);
};

const onOpen = (id) => {
  ModalRef.value?.open();
  getData(id);
};

const onClick = (item) => {
  listQuery.value.deviceType = item.deviceType;
  getData();
};

// 切换下拉菜单显示状态
const toggleStatusDropdown = (event) => {
  console.log("toggleStatusDropdown called, current state:", showStatusDropdown.value);
  showStatusDropdown.value = !showStatusDropdown.value;
  console.log("new state:", showStatusDropdown.value);

  if (showStatusDropdown.value) {
    // 关闭其他下拉菜单
    showDirectionDropdown.value = false;
    showDepartmentDropdown.value = false;
    showWorkAreaDropdown.value = false;

    // 初始化临时选中状态（现在支持多选）
    tempSelectedStatus.value = [...listQuery.value.onlineState];

    // 计算下拉菜单位置
    nextTick(() => {
      const filterIcon = event.currentTarget.querySelector(".filter-icon");
      const dropdown = event.currentTarget.querySelector(".custom-dropdown-menu");

      console.log("filterIcon:", filterIcon);
      console.log("dropdown:", dropdown);

      if (filterIcon && dropdown) {
        const rect = filterIcon.getBoundingClientRect();
        dropdown.style.left = `${rect.right - 121}px`; // 右对齐
        dropdown.style.top = `${rect.bottom + 4}px`; // 在图标下方4px
        console.log("dropdown positioned at:", dropdown.style.left, dropdown.style.top);
      }
    });
  }
};

// 切换状态选项（多选模式）
const toggleStatusOption = (status) => {
  if (tempSelectedStatus.value.includes(status)) {
    // 如果已选中，则取消选中
    tempSelectedStatus.value = tempSelectedStatus.value.filter((item) => item !== status);
  } else {
    // 如果未选中，则添加到选中项
    tempSelectedStatus.value.push(status);
  }
};

// 重置选择
const resetSelection = () => {
  // 清空临时选择状态
  tempSelectedStatus.value = [];

  // 立即应用重置，查询全部数据
  listQuery.value.onlineState = [];
  listQuery.value.page = 1;

  // 关闭下拉菜单
  showStatusDropdown.value = false;

  // 调用接口重新获取数据
  console.log("重置筛选，查询全部数据");
  getData();
};

// 确定选择
const confirmSelection = () => {
  // 将临时选择的状态应用到查询参数（多选模式）
  listQuery.value.onlineState = [...tempSelectedStatus.value];

  // 重置到第一页
  listQuery.value.page = 1;

  // 关闭下拉菜单
  showStatusDropdown.value = false;

  // 调用接口重新获取数据
  console.log("调用接口，查询参数:", {
    onlineState: listQuery.value.onlineState,
    page: listQuery.value.page,
    deviceType: listQuery.value.deviceType,
  });

  getData();
};

// ==================== 方向筛选相关方法 ====================
// 切换方向下拉菜单显示状态
const toggleDirectionDropdown = (event) => {
  showDirectionDropdown.value = !showDirectionDropdown.value;

  if (showDirectionDropdown.value) {
    // 关闭其他下拉菜单
    showStatusDropdown.value = false;
    showDepartmentDropdown.value = false;
    showWorkAreaDropdown.value = false;

    // 初始化临时选中状态（现在支持多选）
    tempSelectedDirection.value = [...listQuery.value.direction];

    // 计算下拉菜单位置
    nextTick(() => {
      const filterIcon = event.currentTarget.querySelector(".filter-icon");
      const dropdown = event.currentTarget.querySelector(".custom-dropdown-menu");

      if (filterIcon && dropdown) {
        const rect = filterIcon.getBoundingClientRect();
        dropdown.style.left = `${rect.right - 121}px`;
        dropdown.style.top = `${rect.bottom + 4}px`;
      }
    });
  }
};

// 切换方向选项（多选模式）
const toggleDirectionOption = (direction) => {
  if (tempSelectedDirection.value.includes(direction)) {
    // 如果已选中，则取消选中
    tempSelectedDirection.value = tempSelectedDirection.value.filter((item) => item !== direction);
  } else {
    // 如果未选中，则添加到选中项
    tempSelectedDirection.value.push(direction);
  }
};

// 重置方向选择
const resetDirectionSelection = () => {
  tempSelectedDirection.value = [];
  listQuery.value.direction = [];
  listQuery.value.page = 1;
  showDirectionDropdown.value = false;
  console.log("重置方向筛选，查询全部数据");
  getData();
};

// 确定方向选择
const confirmDirectionSelection = () => {
  // 将临时选择的方向应用到查询参数（多选模式）
  listQuery.value.direction = [...tempSelectedDirection.value];

  listQuery.value.page = 1;
  showDirectionDropdown.value = false;

  console.log("方向筛选，查询参数:", {
    direction: listQuery.value.direction,
    page: listQuery.value.page,
  });

  getData();
};

// ==================== 分部筛选相关方法 ====================
// 切换分部下拉菜单显示状态
const toggleDepartmentDropdown = (event) => {
  showDepartmentDropdown.value = !showDepartmentDropdown.value;

  if (showDepartmentDropdown.value) {
    // 关闭其他下拉菜单
    showStatusDropdown.value = false;
    showDirectionDropdown.value = false;
    showWorkAreaDropdown.value = false;

    // 初始化临时选中状态（现在支持多选）
    tempSelectedDepartment.value = [...listQuery.value.departmentId];

    // 计算下拉菜单位置
    nextTick(() => {
      const filterIcon = event.currentTarget.querySelector(".filter-icon");
      const dropdown = event.currentTarget.querySelector(".custom-dropdown-menu");

      if (filterIcon && dropdown) {
        const rect = filterIcon.getBoundingClientRect();
        dropdown.style.left = `${rect.right - 121}px`;
        dropdown.style.top = `${rect.bottom + 4}px`;
      }
    });
  }
};

// 切换分部选项（多选模式）
const toggleDepartmentOption = (department) => {
  if (tempSelectedDepartment.value.includes(department)) {
    // 如果已选中，则取消选中
    tempSelectedDepartment.value = tempSelectedDepartment.value.filter(
      (item) => item !== department
    );
  } else {
    // 如果未选中，则添加到选中项
    tempSelectedDepartment.value.push(department);
  }
};

// 重置分部选择
const resetDepartmentSelection = () => {
  tempSelectedDepartment.value = [];
  listQuery.value.departmentId = [];
  listQuery.value.page = 1;
  showDepartmentDropdown.value = false;
  console.log("重置分部筛选，查询全部数据");
  getData();
};

// 确定分部选择
const confirmDepartmentSelection = () => {
  // 将临时选择的分部应用到查询参数（多选模式）
  listQuery.value.departmentId = [...tempSelectedDepartment.value];

  listQuery.value.page = 1;
  showDepartmentDropdown.value = false;

  console.log("分部筛选，查询参数:", {
    departmentId: listQuery.value.departmentId,
    page: listQuery.value.page,
  });

  getData();
};

// ==================== 工区筛选相关方法 ====================
// 切换工区下拉菜单显示状态
const toggleWorkAreaDropdown = (event) => {
  showWorkAreaDropdown.value = !showWorkAreaDropdown.value;

  if (showWorkAreaDropdown.value) {
    // 关闭其他下拉菜单
    showStatusDropdown.value = false;
    showDirectionDropdown.value = false;
    showDepartmentDropdown.value = false;

    // 初始化临时选中状态（现在支持多选）
    tempSelectedWorkArea.value = [...listQuery.value.workAreaId];

    // 计算下拉菜单位置
    nextTick(() => {
      const filterIcon = event.currentTarget.querySelector(".filter-icon");
      const dropdown = event.currentTarget.querySelector(".custom-dropdown-menu");

      if (filterIcon && dropdown) {
        const rect = filterIcon.getBoundingClientRect();
        dropdown.style.left = `${rect.right - 121}px`;
        dropdown.style.top = `${rect.bottom + 4}px`;
      }
    });
  }
};

// 切换工区选项（多选模式）
const toggleWorkAreaOption = (workArea) => {
  if (tempSelectedWorkArea.value.includes(workArea)) {
    // 如果已选中，则取消选中
    tempSelectedWorkArea.value = tempSelectedWorkArea.value.filter((item) => item !== workArea);
  } else {
    // 如果未选中，则添加到选中项
    tempSelectedWorkArea.value.push(workArea);
  }
};

// 重置工区选择
const resetWorkAreaSelection = () => {
  tempSelectedWorkArea.value = [];
  listQuery.value.workAreaId = [];
  listQuery.value.page = 1;
  showWorkAreaDropdown.value = false;
  console.log("重置工区筛选，查询全部数据");
  getData();
};

// 确定工区选择
const confirmWorkAreaSelection = () => {
  // 将临时选择的工区应用到查询参数（多选模式）
  listQuery.value.workAreaId = [...tempSelectedWorkArea.value];

  listQuery.value.page = 1;
  showWorkAreaDropdown.value = false;

  console.log("工区筛选，查询参数:", {
    workAreaId: listQuery.value.workAreaId,
    page: listQuery.value.page,
  });

  getData();
};

// 点击外部关闭下拉菜单
const closeDropdown = (event) => {
  // 检查点击的元素是否在下拉菜单内
  const dropdown = event.target.closest(".custom-dropdown");
  if (!dropdown) {
    showStatusDropdown.value = false;
    showDirectionDropdown.value = false;
    showDepartmentDropdown.value = false;
    showWorkAreaDropdown.value = false;
  }
};

// 监听点击外部事件
onMounted(() => {
  document.addEventListener("click", closeDropdown);
});

onUnmounted(() => {
  document.removeEventListener("click", closeDropdown);
});

defineExpose({
  open: onOpen,
});
</script>

<style lang="scss" scoped>
.icon-title {
  width: 28px;
  height: 28px;
  margin-right: 12px;
}

.statics {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;

  &-item {
    flex: 1;
    min-width: 180px;
    max-width: 250px;
    height: clamp(90px, 12vh, 130px);
    position: relative;
    padding: 8px;
    display: flex;
    align-items: center;
    cursor: pointer;

    &-icon-container {
      height: 100%;
      width: clamp(80px, 10vw, 120px);
      aspect-ratio: 1;
      background-image: url("@/assets/modules/traffic/icon/device-bg.svg");
      background-repeat: no-repeat;
      background-size: 100% auto;
      background-position: 0 bottom;
      position: relative;
    }

    &-icon {
      position: absolute;
      left: 50%;
      top: 0;
      transform: translateX(-50%);
      width: clamp(40px, 6vw, 70px);
      height: clamp(40px, 6vw, 70px);
      margin-left: auto;
    }

    &-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: baseline;

      &-total {
        display: flex;
        align-items: center;
      }

      &-number {
        width: auto;
        min-width: 40px;
        font-family: D-DIN-PRO;
        font-weight: bold;
        font-size: clamp(24px, 3vw, 40px);
        color: #ffffff;
      }

      &-unit {
        font-family: Alibaba PuHuiTi;
        font-size: clamp(14px, 1.8vw, 22px);
        color: #ffffff;
      }

      &-label {
        font-family: Alibaba PuHuiTi;
        font-size: clamp(14px, 1.8vw, 22px);
        color: #ffffff;
      }
    }

    &.is-active {
      .border {
        position: absolute;
        width: 26px;
        height: 26px;
        background-color: #00f6ff;
        --border-width: 1px;
        // border: 1px solid;
        // border-image: linear-gradient(315deg, rgba(0, 246, 255, 0) 50%, rgba(0, 246, 255, 1)) 1 1;

        &-left-top {
          left: 0px;
          top: 0px;
          clip-path: polygon(
            0 0,
            100% 0,
            100% var(--border-width),
            var(--border-width) var(--border-width),
            var(--border-width) 100%,
            0 100%
          );
        }

        &-left-bottom {
          left: 0px;
          bottom: 0px;
          clip-path: polygon(
            0 0,
            var(--border-width) 0,
            var(--border-width) calc(100% - var(--border-width)),
            100% calc(100% - var(--border-width)),
            100% 100%,
            0 100%
          );
        }

        &-right-top {
          right: 0px;
          top: 0px;
          clip-path: polygon(
            0 0,
            100% 0,
            100% 100%,
            calc(100% - var(--border-width)) 100%,
            calc(100% - var(--border-width)) var(--border-width),
            0 var(--border-width)
          );
        }

        &-right-bottom {
          right: 0px;
          bottom: 0px;
          clip-path: polygon(
            calc(100% - var(--border-width)) 0,
            100% 0,
            100% 100%,
            0 100%,
            0 calc(100% - var(--border-width)),
            calc(100% - var(--border-width)) calc(100% - var(--border-width))
          );
        }
      }

      .dot {
        position: absolute;
        width: 4px;
        height: 4px;
        box-shadow: 0px 0px 13px 0px #33c3ff, 0px 0px 13px 0px #33c3ff;
        border: 1px solid #35daf4;

        &-left-top {
          left: 12px;
          top: 12px;
        }

        &-right-bottom {
          right: 12px;
          bottom: 12px;
        }
      }

      &::after {
        content: "";
        position: absolute;
        left: 4px;
        top: 4px;
        width: calc(100% - 8px);
        height: calc(100% - 8px);
        background-color: rgba(0, 138, 255, 0.09);
        box-sizing: border-box;
        border: 1px solid;
        border-image: linear-gradient(180deg, rgba(0, 111, 175, 0), rgba(0, 111, 175, 1)) 1 1;
      }
    }
  }
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px 0;
  min-height: 0; // 确保flex子项能正确收缩

  .table-title {
    height: auto;
    min-height: 31px;
    padding: clamp(16px, 2.5vh, 24px) 0;
    font-family: Alibaba PuHuiTi;
    font-size: clamp(16px, 2.2vw, 24px);
    letter-spacing: 2px;
    color: #ffffff;
    background-image: linear-gradient(180deg, #dff0fa 19%, #e0f0ff 46%, #a8d4f9 82%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    flex-shrink: 0; // 标题不收缩

    .icon-divider {
      position: absolute;
      left: 0;
      bottom: 8px;
      width: 100%;
      height: auto;
    }
  }

  .table-content {
    flex: 1;
    min-height: 0; // 确保表格内容能正确收缩
    overflow: hidden;
  }

  :deep(.arco-table) {
    height: 100%;

    .arco-table-container {
      border: none;
      height: 100%;
    }

    .arco-table-body {
      background-color: transparent;
    }

    .arco-table-th {
      font-family: Alibaba PuHuiTi;
      font-weight: normal;
      font-size: 20px;
      color: #ffffff;
      background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
        rgba(0, 138, 255, 0.3);
      border-image: linear-gradient(
          90deg,
          rgba(0, 138, 255, 0),
          rgba(0, 138, 255, 1),
          rgba(0, 138, 255, 0.2),
          rgba(0, 138, 255, 0)
        )
        1 1;
    }

    .arco-table-td {
      font-family: Alibaba PuHuiTi;
      background-color: transparent;
      border-bottom: 1px solid rgba(0, 138, 255, 0.4);
      font-weight: normal;
      font-size: 16px;
      color: #ffffff;
      text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
      font-style: normal;
      text-transform: none;

      /* &:hover {
        background: rgba(0, 138, 255, 0.05) !important;
      } */
    }

    .arco-table-border:not(.arco-table-border-cell) .arco-table-container {
      border: none;
    }
    .arco-table-tr:hover {
      background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
        rgba(0, 138, 255, 0.05);
      box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
      border-radius: 0px 0px 0px 0px;
    }
    .arco-scrollbar-thumb-direction-vertical .arco-scrollbar-thumb-bar {
      background: linear-gradient(90deg, #00d2ff 0%, rgba(0, 210, 255, 0) 100%), #3168ff;
      box-shadow: 0px 0px 50px 0px #0072ff;
      border-radius: 8px 8px 8px 8px;
    }
    .arco-scrollbar-thumb-direction-horizontal .arco-scrollbar-thumb-bar {
      background: linear-gradient(90deg, #00d2ff 0%, rgba(0, 210, 255, 0) 100%), #3168ff;
      box-shadow: 0px 0px 50px 0px #0072ff;
      border-radius: 8px 8px 8px 8px;
    }
    .arco-table-cell {
      padding: 0;
    }
  }
}

/* 设备状态标题样式 */
.status-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 自定义下拉菜单容器 */
.custom-dropdown {
  position: relative;
  display: inline-block;
  margin-bottom: -4px;
}

.filter-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

/* 自定义下拉菜单样式 */
.custom-dropdown-menu {
  position: fixed;
  top: auto;
  right: auto;
  width: 121px;
  min-height: 130px;
  background: linear-gradient(
    90deg,
    rgba(0, 138, 255, 0.08) 0%,
    rgba(0, 138, 255, 0.12) 50%,
    rgba(0, 138, 255, 0.08) 100%
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 4px;
  border: 1px solid;
  border-image: linear-gradient(162deg, rgba(27, 112, 235, 0.6), rgba(27, 112, 235, 0)) 1 1;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dropdown-options {
  flex: 1;
  padding: 8px 0;
}

.custom-dropdown-option {
  padding: 5px 5px;
  color: #ffffff;
  font-family: Alibaba PuHuiTi;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;

  /* &:hover {
    background: rgba(0, 138, 255, 0.1);
  } */

  &.selected {
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
      rgba(0, 138, 255, 0.05);
    box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid;
    border-image: linear-gradient(
        90deg,
        rgba(0, 138, 255, 0),
        rgba(160, 212, 255, 1),
        rgba(0, 138, 255, 0)
      )
      1 1;
  }
}

.option-checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #4a90e2;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &.checked {
    background: #4a90e2;
    border-color: #4a90e2;
  }
}

.check-icon {
  width: 12px;
  height: 12px;
  fill: #ffffff;
}

.option-text {
  color: #ffffff;
  font-size: 14px;
  font-family: Alibaba PuHuiTi;
}

.dropdown-actions {
  display: flex;
  gap: 8px;
  padding: 5px;
  background: rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(0, 138, 255, 0.2);
}

.action-btn {
  flex: 1;
  height: 25px;
  /* border: 3px solid; */
  color: #ffffff;
  font-family: Alibaba PuHuiTi;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  /* border-radius: 6px; */
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  &.reset-btn {
    /* border: 1px solid rgba(0, 138, 255, 0.3); */
    /* border-radius: 8px; */
    background: linear-gradient(180deg, #1e3a5f 0%, #2d4a6b 100%);
    border-image: linear-gradient(162deg, rgba(27, 112, 235, 0.6), rgba(27, 112, 235, 0)) 1 1;
    color: #ffffff;

    &:hover {
      background: linear-gradient(180deg, #2a4a75 0%, #3a5a81 100%);
    }
  }

  &.confirm-btn {
    background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
    border-image: linear-gradient(162deg, rgba(74, 144, 226, 0.8), rgba(74, 144, 226, 0)) 1 1;
    color: #ffffff;
    /* box-shadow: 0px 2px 4px rgba(74, 144, 226, 0.3); */

    &:hover {
      background: linear-gradient(180deg, #5ba0f2 0%, #4689d1 100%);
      /* box-shadow: 0px 2px 6px rgba(74, 144, 226, 0.4); */
    }
  }
}

/* 设备状态样式 */
.device-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;

  &.status-online {
    background: #27f3bd;
    box-shadow: 0px 0px 8px 2px rgba(41, 255, 198, 0.6);
  }

  &.status-offline {
    background: #ff4757;
    box-shadow: 0px 0px 8px 2px rgba(255, 71, 87, 0.6);
  }
}

.status-text {
  font-family: Alibaba PuHuiTi;
  font-size: 16px;
  font-weight: normal;

  &.text-online {
    color: #27f3bd;
  }

  &.text-offline {
    color: #ff4757;
  }
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(30, 60, 100, 0.05);
  border-top: 1px solid rgba(0, 138, 255, 0.2);
  font-family: Alibaba PuHuiTi;
  flex-shrink: 0; // 分页区域不收缩，固定在底部
}

.pagination-info {
  font-size: clamp(12px, 1.4vw, 16px);
  color: #8a9ba8;

  .pagination-number {
    color: #008aff;
    font-weight: 500;
  }
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  min-width: clamp(28px, 3.5vw, 36px);
  height: clamp(28px, 3.5vw, 36px);
  padding: 0 clamp(6px, 1vw, 10px);
  border: 1px solid rgba(0, 138, 255, 0.3);
  background: transparent;
  color: #8a9ba8;
  font-size: clamp(12px, 1.4vw, 16px);
  font-family: Alibaba PuHuiTi;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled):not(.ellipsis) {
    border-color: #008aff;
    color: #c4e5ff;
    background: rgba(0, 138, 255, 0.1);
  }

  &.active {
    border-color: #008aff;
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 0%, #008aff 100%);
    color: #ffffff;
    box-shadow: 0px 0px 8px 0px rgba(0, 138, 255, 0.4);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &.ellipsis {
    cursor: default;
    border: none;
    background: transparent;

    &:hover {
      border: none;
      background: transparent;
      color: #8a9ba8;
    }
  }
}
</style>
